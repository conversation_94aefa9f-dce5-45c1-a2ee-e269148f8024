import React, { useRef, useEffect, useState } from 'react';
import * as PIXI from 'pixi.js';

// 🎨 PixiJS 高性能工况图组件
const Lmh2 = () => {
    const canvasRef = useRef(null);
    const appRef = useRef(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    // 监听容器尺寸变化
    useEffect(() => {
        const updateDimensions = () => {
            if (canvasRef.current) {
                const { clientWidth, clientHeight } = canvasRef.current;
                setDimensions({ width: clientWidth, height: clientHeight });
            }
        };

        // 初始化尺寸
        updateDimensions();

        // 监听窗口大小变化
        window.addEventListener('resize', updateDimensions);

        // 使用 ResizeObserver 监听容器大小变化
        const resizeObserver = new ResizeObserver(updateDimensions);
        if (canvasRef.current) {
            resizeObserver.observe(canvasRef.current);
        }

        return () => {
            window.removeEventListener('resize', updateDimensions);
            resizeObserver.disconnect();
        };
    }, []);

    useEffect(() => {
        if (dimensions.width === 0 || dimensions.height === 0) return;

        // 创建 PIXI 应用实例 - 使用新的 PixiJS 8.x API
        const app = new PIXI.Application();

        // 初始化应用
        const initApp = async () => {
            await app.init({
                width: dimensions.width,
                height: dimensions.height,
                backgroundColor: 0x000000, // 透明背景
                backgroundAlpha: 0, // 设置背景透明度为0
                antialias: true,
                resolution: window.devicePixelRatio || 1,
                autoDensity: true,
            });

            appRef.current = app;

            // 使用新的 canvas API 替代 view
            if (canvasRef.current && app.canvas) {
                canvasRef.current.appendChild(app.canvas);
                // 设置 canvas 样式以适应容器
                app.canvas.style.width = '100%';
                app.canvas.style.height = '100%';
                app.canvas.style.objectFit = 'contain';
            }

            // 🎯 绘制工况图的各个组件
            drawFlowChart(app, dimensions);
        };

        initApp().catch(console.error);

        // 清理函数
        return () => {
            if (appRef.current) {
                // 确保正确清理
                if (canvasRef.current && appRef.current.canvas) {
                    canvasRef.current.removeChild(appRef.current.canvas);
                }
                appRef.current.destroy(true, {
                    children: true,
                    texture: true,
                    baseTexture: true
                });
                appRef.current = null;
            }
        };
    }, [dimensions]);

    // 🎨 绘制完整的工况图
    const drawFlowChart = (app, dimensions) => {
        // 计算缩放比例 - 基于原始设计尺寸 1400x400
        const originalWidth = 1400;
        const originalHeight = 400;
        const scaleX = dimensions.width / originalWidth;
        const scaleY = dimensions.height / originalHeight;
        const scale = Math.min(scaleX, scaleY);

        // 创建主容器并应用缩放
        const mainContainer = new PIXI.Container();
        mainContainer.scale.set(scale);

        // 居中显示
        mainContainer.x = (dimensions.width - originalWidth * scale) / 2;
        mainContainer.y = (dimensions.height - originalHeight * scale) / 2;

        app.stage.addChild(mainContainer);

        const graphics = new PIXI.Graphics();
        mainContainer.addChild(graphics);

        // 🔗 绘制主管道系统
        drawPipelineSystem(graphics);

        // 📊 绘制仪表组件
        drawInstruments(mainContainer);

        // ⚙️ 绘制阀门组件
        drawValves(mainContainer);
    };

    // 🔗 绘制管道系统
    const drawPipelineSystem = (graphics) => {
        // 使用新的线条样式API
        graphics.setStrokeStyle({
            width: 3,
            color: 0x00ff88,
            alpha: 1
        });

        // 主水平管道
        graphics.moveTo(150, 200);
        graphics.lineTo(1250, 200);
        graphics.stroke();

        // 左侧进口管道
        graphics.moveTo(90, 160);
        graphics.lineTo(150, 160);
        graphics.lineTo(150, 200);
        graphics.stroke();

        // 左侧压力计管道
        graphics.moveTo(90, 240);
        graphics.lineTo(150, 240);
        graphics.lineTo(150, 200);
        graphics.stroke();

        // 右侧出口管道
        graphics.moveTo(1250, 200);
        graphics.lineTo(1310, 200);
        graphics.lineTo(1310, 160);
        graphics.lineTo(1360, 160);
        graphics.stroke();

        // 右侧压力计管道
        graphics.moveTo(1250, 200);
        graphics.lineTo(1310, 200);
        graphics.lineTo(1310, 240);
        graphics.lineTo(1360, 240);
        graphics.stroke();

        // 阀门连接管道
        for (let i = 0; i < 6; i++) {
            const x = 300 + i * 150;
            graphics.moveTo(x, 200);
            graphics.lineTo(x, 150);
            graphics.stroke();
        }

        // 绘制连接节点
        graphics.setStrokeStyle({ width: 0 });
        graphics.setFillStyle({ color: 0x00ff88 });

        // 主要连接点
        const connectionPoints = [
            [150, 200], [300, 200], [450, 200], [600, 200],
            [750, 200], [900, 200], [1050, 200], [1250, 200]
        ];

        connectionPoints.forEach(([x, y]) => {
            graphics.circle(x, y, 4);
            graphics.fill();
        });
    };

    // 📊 绘制仪表组件
    const drawInstruments = (app) => {
        // 🌊 左侧进口流量计
        createFlowMeter(app, 50, 120, '进口流量计', '127.38', 'm³/h', '1,234,569.13 m³');

        // 🔽 左侧进口压力计
        createPressureMeter(app, 50, 260, '进口压力计', '0.59', 'MPa');

        // 🌊 右侧出口流量计
        createFlowMeter(app, 1300, 120, '出口流量计', '113.04', 'm³/h', '1,102,103.24 m³');

        // 🔽 右侧出口压力计
        createPressureMeter(app, 1300, 260, '出口压力计', '0.87', 'MPa');
    };

    // ⚙️ 绘制阀门组件
    const drawValves = (app) => {
        const valveNames = ['一号泵', '二号泵', '三号泵', '四号泵', '五号泵', '六号泵'];

        valveNames.forEach((name, index) => {
            const x = 300 + index * 150;
            const y = 120;
            createValve(app, x, y, name);
        });
    };

    // 🌊 创建流量计组件
    const createFlowMeter = (app, x, y, title, mainValue, unit, totalValue) => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框
        const bg = new PIXI.Graphics();
        bg.setFillStyle({ color: 0x2a2a2a, alpha: 0.9 });
        bg.setStrokeStyle({ width: 2, color: 0x00ff88 });
        bg.roundRect(-80, -30, 160, 60, 8);
        bg.fill();
        bg.stroke();
        container.addChild(bg);

        // 流量计图标
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x00ff88 });
        icon.circle(-50, 0, 15);
        icon.fill();

        // 添加刻度线
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const startX = -50 + Math.cos(angle) * 10;
            const startY = Math.sin(angle) * 10;
            const endX = -50 + Math.cos(angle) * 12;
            const endY = Math.sin(angle) * 12;

            icon.setStrokeStyle({ width: 1, color: 0x1a1a1a });
            icon.moveTo(startX, startY);
            icon.lineTo(endX, endY);
            icon.stroke();
        }
        container.addChild(icon);

        // 主数值
        const mainText = new PIXI.Text({
            text: `${mainValue} ${unit}`,
            style: {
                fontFamily: 'Arial',
                fontSize: 14,
                fill: 0x00ff88,
                fontWeight: 'bold',
            }
        });
        mainText.x = -20;
        mainText.y = -10;
        container.addChild(mainText);

        // 累计值
        const totalText = new PIXI.Text({
            text: totalValue,
            style: {
                fontFamily: 'Arial',
                fontSize: 10,
                fill: 0xcccccc,
            }
        });
        totalText.x = -20;
        totalText.y = 8;
        container.addChild(totalText);

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 10,
                fill: 0xcccccc,
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -50;
        container.addChild(titleText);

        app.stage.addChild(container);
    };

    // 🔽 创建压力计组件
    const createPressureMeter = (app, x, y, title, value, unit) => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框
        const bg = new PIXI.Graphics();
        bg.setFillStyle({ color: 0x2a2a2a, alpha: 0.9 });
        bg.setStrokeStyle({ width: 2, color: 0x4a9eff });
        bg.roundRect(-60, -25, 120, 50, 8);
        bg.fill();
        bg.stroke();
        container.addChild(bg);

        // 压力计图标
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x4a9eff });
        icon.circle(-30, 0, 12);
        icon.fill();

        // 指针
        icon.setStrokeStyle({ width: 2, color: 0x1a1a1a });
        icon.moveTo(-30, 0);
        icon.lineTo(-30 + 8, -5);
        icon.stroke();
        container.addChild(icon);

        // 数值文本
        const valueText = new PIXI.Text({
            text: `${value} ${unit}`,
            style: {
                fontFamily: 'Arial',
                fontSize: 14,
                fill: 0x4a9eff,
                fontWeight: 'bold',
            }
        });
        valueText.x = -10;
        valueText.y = -7;
        container.addChild(valueText);

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 10,
                fill: 0xcccccc,
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -45;
        container.addChild(titleText);

        app.stage.addChild(container);
    };

    // ⚙️ 创建阀门组件
    const createValve = (app, x, y, name) => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 阀门背景
        const bg = new PIXI.Graphics();
        bg.setFillStyle({ color: 0x2a2a2a, alpha: 0.9 });
        bg.setStrokeStyle({ width: 2, color: 0xffffff });
        bg.roundRect(-30, -30, 60, 60, 8);
        bg.fill();
        bg.stroke();
        container.addChild(bg);

        // 风扇叶片
        const fan = new PIXI.Graphics();
        fan.setFillStyle({ color: 0xffffff });

        // 绘制4个扇叶
        for (let i = 0; i < 4; i++) {
            const angle = (i / 4) * Math.PI * 2;
            const x1 = Math.cos(angle) * 8;
            const y1 = Math.sin(angle) * 8;
            const x2 = Math.cos(angle + Math.PI / 6) * 15;
            const y2 = Math.sin(angle + Math.PI / 6) * 15;
            const x3 = Math.cos(angle - Math.PI / 6) * 15;
            const y3 = Math.sin(angle - Math.PI / 6) * 15;

            fan.moveTo(0, 0);
            fan.lineTo(x1, y1);
            fan.lineTo(x2, y2);
            fan.lineTo(x3, y3);
            fan.lineTo(0, 0);
        }

        fan.fill();
        container.addChild(fan);

        // 中心圆
        const center = new PIXI.Graphics();
        center.setFillStyle({ color: 0x2a2a2a });
        center.circle(0, 0, 4);
        center.fill();
        container.addChild(center);

        // 阀门名称
        const nameText = new PIXI.Text({
            text: name,
            style: {
                fontFamily: 'Arial',
                fontSize: 10,
                fill: 0xffffff,
            }
        });
        nameText.x = -nameText.width / 2;
        nameText.y = 40;
        container.addChild(nameText);

        app.stage.addChild(container);
    };

    return (
        <div className="w-full h-full relative">
            <div
                ref={canvasRef}
                className="w-full h-full"
            />
        </div>
    );
};

export default Lmh2;
